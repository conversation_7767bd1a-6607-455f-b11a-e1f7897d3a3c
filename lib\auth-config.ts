/**
 * Enhanced authentication configuration for route protection
 * Designed to support RBAC and multi-tenant functionality
 */

// User roles for RBAC
export enum UserRole {
  INDIVIDUAL_USER = "individual_user", // Maritime professionals (commercial shipping, offshore, cruise, recreational boating, etc.)
  CERT_PROVIDER = "cert_provider",
  YACHT_COMPANY = "yacht_company",
  SYSTEM_ADMIN = "system_admin"
}

// Legacy role mapping for backward compatibility (session migration only)
// Note: This is only used for migrating existing sessions, not for new users
export const LEGACY_ROLE_MAPPING = {
  "legacy_maritime_user": UserRole.INDIVIDUAL_USER // Legacy role mapping
} as const

// Individual user subscription plans (for maritime professionals)
export enum IndividualSubscriptionPlan {
  FREE = "individual_free",
  BASIC = "individual_basic",
  PREMIUM = "individual_premium",
  PROFESSIONAL = "individual_professional"
}

// Certification provider subscription plans (B2B focused)
export enum ProviderSubscriptionPlan {
  STARTER = "provider_starter",
  PROFESSIONAL = "provider_professional",
  ENTERPRISE = "provider_enterprise"
}

// Yacht company subscription plans (corporate focused)
export enum CompanySubscriptionPlan {
  BASIC = "company_basic",
  PROFESSIONAL = "company_professional",
  ENTERPRISE = "company_enterprise",
  FLEET = "company_fleet"
}

// Unified subscription plan type for session context
export type SubscriptionPlan =
  | IndividualSubscriptionPlan
  | ProviderSubscriptionPlan
  | CompanySubscriptionPlan
  | "system_admin" // System admins don't have subscription plans

// Permissions for granular access control
export enum Permission {
  // Certificate permissions
  CERTIFICATES_READ = "certificates:read",
  CERTIFICATES_WRITE = "certificates:write",
  CERTIFICATES_DELETE = "certificates:delete",
  CERTIFICATES_BULK_OPERATIONS = "certificates:bulk",

  // User management permissions
  USERS_READ = "users:read",
  USERS_WRITE = "users:write",
  USERS_DELETE = "users:delete",

  // Tenant management permissions
  TENANT_ADMIN = "tenant:admin",
  TENANT_READ = "tenant:read",

  // System permissions
  SYSTEM_ADMIN = "system:admin",
  ANALYTICS_READ = "analytics:read",

  // Premium features
  ADVANCED_REPORTING = "features:advanced_reporting",
  API_ACCESS = "features:api_access",
  BULK_EXPORT = "features:bulk_export"
}

// Session context for multi-tenant support
export interface SessionContext {
  userId: string
  email: string
  name: string
  role: UserRole
  subscriptionPlan: SubscriptionPlan
  permissions: Permission[]
  emailVerified: boolean // Email verification status
  tenantId?: string // For multi-tenant isolation
  tenantRole?: string // Role within the tenant
}

// Enhanced route configuration
export interface RouteConfig {
  pattern: string | RegExp
  requiresAuth: boolean
  allowedRoles?: UserRole[]
  requiredPermissions?: Permission[]
  minimumPlan?: SubscriptionPlan
  tenantRequired?: boolean
  redirectTo?: string
  description?: string
  // Future: rate limiting, feature flags, etc.
  rateLimit?: {
    requests: number
    windowMs: number
  }
}

/**
 * Route configuration for the application
 * Routes are checked in order - first match wins
 */
export const routeConfig: RouteConfig[] = [
  // Public routes (no authentication required)
  {
    pattern: "/",
    requiresAuth: false,
    description: "Landing page"
  },
  {
    pattern: /^\/login/,
    requiresAuth: false,
    description: "Login pages"
  },
  {
    pattern: /^\/signup/,
    requiresAuth: false,
    description: "Signup pages"
  },
  {
    pattern: /^\/auth\//,
    requiresAuth: false,
    description: "Auth callback routes"
  },
  {
    pattern: /^\/api\/auth\//,
    requiresAuth: false,
    description: "Auth API routes"
  },
  {
    pattern: /^\/verify-email/,
    requiresAuth: false,
    description: "Email verification page"
  },
  {
    pattern: /^\/verification-pending/,
    requiresAuth: false,
    description: "Email verification pending page"
  },
  {
    pattern: /^\/forgot-password/,
    requiresAuth: false,
    description: "Forgot password page"
  },
  {
    pattern: /^\/reset-password/,
    requiresAuth: false,
    description: "Reset password page"
  },

  // Basic authenticated routes (all user types)
  {
    pattern: /^\/dashboard/,
    requiresAuth: true,
    description: "Dashboard pages"
  },
  {
    pattern: /^\/certificates/,
    requiresAuth: true,
    requiredPermissions: [Permission.CERTIFICATES_READ],
    description: "Certificate management"
  },
  {
    pattern: /^\/profile/,
    requiresAuth: true,
    description: "User profile"
  },
  {
    pattern: /^\/settings/,
    requiresAuth: true,
    description: "Application settings"
  },
  {
    pattern: /^\/notifications/,
    requiresAuth: true,
    description: "Notifications"
  },
  {
    pattern: /^\/help/,
    requiresAuth: true,
    description: "Help pages"
  },

  // Future: Premium features
  {
    pattern: /^\/analytics/,
    requiresAuth: true,
    requiredPermissions: [Permission.ANALYTICS_READ],
    minimumPlan: IndividualSubscriptionPlan.PREMIUM, // Example for individual users
    description: "Analytics dashboard"
  },

  // Future: Admin routes for certification providers
  {
    pattern: /^\/admin\/provider/,
    requiresAuth: true,
    allowedRoles: [UserRole.CERT_PROVIDER, UserRole.SYSTEM_ADMIN],
    tenantRequired: true,
    description: "Certification provider admin"
  },

  // Future: Company admin routes
  {
    pattern: /^\/admin\/company/,
    requiresAuth: true,
    allowedRoles: [UserRole.YACHT_COMPANY, UserRole.SYSTEM_ADMIN],
    tenantRequired: true,
    description: "Yacht company admin"
  },

  // System admin routes (Phase 5 implementation)
  {
    pattern: /^\/admin/,
    requiresAuth: true,
    allowedRoles: [UserRole.SYSTEM_ADMIN],
    requiredPermissions: [Permission.SYSTEM_ADMIN],
    description: "Admin dashboard and system administration"
  },

  // Default: require authentication for all other routes
  {
    pattern: /.*/,
    requiresAuth: true,
    description: "Default protection for all other routes"
  }
]

/**
 * Check if a route requires authentication and validate permissions
 */
export function getRouteConfig(pathname: string): RouteConfig | null {
  for (const config of routeConfig) {
    if (typeof config.pattern === 'string') {
      if (pathname === config.pattern) {
        return config
      }
    } else {
      if (config.pattern.test(pathname)) {
        return config
      }
    }
  }
  return null
}

/**
 * Check if user has required permissions for a route
 * Currently simplified - will be enhanced when RBAC is implemented
 */
export function hasRouteAccess(
  routeConfig: RouteConfig,
  sessionContext?: SessionContext
): boolean {
  // Basic auth check
  if (routeConfig.requiresAuth && !sessionContext) {
    return false
  }

  // For now, if user is authenticated, they have access
  // Future: implement role, permission, and subscription checks
  if (sessionContext) {
    // TODO: Implement when RBAC is added
    // - Check allowedRoles
    // - Check requiredPermissions
    // - Check minimumPlan
    // - Check tenantRequired
    return true
  }

  return !routeConfig.requiresAuth
}

/**
 * Get default redirect URL based on user role and context
 * Currently returns dashboard - will be enhanced for multi-tenant
 */
export function getDefaultRedirect(sessionContext?: SessionContext): string {
  if (!sessionContext) {
    return authUrls.login
  }

  // Future: role-based default redirects
  // switch (sessionContext.role) {
  //   case UserRole.CERT_PROVIDER:
  //     return `/admin/provider/${sessionContext.tenantId}`
  //   case UserRole.YACHT_COMPANY:
  //     return `/admin/company/${sessionContext.tenantId}`
  //   case UserRole.SYSTEM_ADMIN:
  //     return '/admin/system'
  //   default:
  //     return '/dashboard'
  // }

  return authUrls.dashboard
}

/**
 * Auth redirect URLs
 */
export const authUrls = {
  login: "/login",
  signIn: "/login",
  dashboard: "/dashboard",
  afterSignIn: "/dashboard",
  defaultRedirect: "/dashboard",
  verifyEmail: "/verify-email",
  verificationPending: "/verification-pending",
  // Future: role-specific defaults
  adminProvider: "/admin/provider",
  adminCompany: "/admin/company",
  adminSystem: "/admin/system"
} as const

/**
 * Default permissions by role
 * Will be used when implementing RBAC
 */
export const defaultPermissionsByRole: Record<UserRole, Permission[]> = {
  [UserRole.INDIVIDUAL_USER]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE
  ],
  [UserRole.CERT_PROVIDER]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.USERS_READ,
    Permission.USERS_WRITE,
    Permission.TENANT_ADMIN,
    Permission.ANALYTICS_READ
  ],
  [UserRole.YACHT_COMPANY]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ,
    Permission.ANALYTICS_READ
  ],
  [UserRole.SYSTEM_ADMIN]: [
    ...Object.values(Permission)
  ]
}

/**
 * Individual user subscription plan features
 * For maritime professionals (yachting, commercial shipping, offshore, cruise, recreational boating, etc.)
 */
export const individualPlanFeatures: Record<IndividualSubscriptionPlan, Permission[]> = {
  [IndividualSubscriptionPlan.FREE]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE
  ],
  [IndividualSubscriptionPlan.BASIC]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE
  ],
  [IndividualSubscriptionPlan.PREMIUM]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT
  ],
  [IndividualSubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_DELETE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT,
    Permission.API_ACCESS
  ]
}

/**
 * Certification provider subscription plan features
 * For organizations that issue certificates
 */
export const providerPlanFeatures: Record<ProviderSubscriptionPlan, Permission[]> = {
  [ProviderSubscriptionPlan.STARTER]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.USERS_READ,
    Permission.TENANT_READ
  ],
  [ProviderSubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.USERS_READ,
    Permission.USERS_WRITE,
    Permission.TENANT_ADMIN,
    Permission.ANALYTICS_READ
  ],
  [ProviderSubscriptionPlan.ENTERPRISE]: [
    Permission.CERTIFICATES_READ,
    Permission.CERTIFICATES_WRITE,
    Permission.CERTIFICATES_BULK_OPERATIONS,
    Permission.USERS_READ,
    Permission.USERS_WRITE,
    Permission.TENANT_ADMIN,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.API_ACCESS
  ]
}

/**
 * Yacht company subscription plan features
 * For businesses that need to verify employee certificates
 */
export const companyPlanFeatures: Record<CompanySubscriptionPlan, Permission[]> = {
  [CompanySubscriptionPlan.BASIC]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ
  ],
  [CompanySubscriptionPlan.PROFESSIONAL]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ,
    Permission.ANALYTICS_READ
  ],
  [CompanySubscriptionPlan.ENTERPRISE]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING
  ],
  [CompanySubscriptionPlan.FLEET]: [
    Permission.CERTIFICATES_READ,
    Permission.USERS_READ,
    Permission.TENANT_READ,
    Permission.ANALYTICS_READ,
    Permission.ADVANCED_REPORTING,
    Permission.BULK_EXPORT,
    Permission.API_ACCESS
  ]
}
