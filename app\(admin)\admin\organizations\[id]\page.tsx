import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-config";
import { getOrganizationById, getOrganizationMembers } from "@/lib/db";
import { getServerSession } from "@/lib/session";
import { ArrowLeft, Users, Calendar, Globe, Mail } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Suspense } from "react";

async function getOrganizationDetails(id: string) {
  try {
    const organization = await getOrganizationById(id);
    if (!organization) {
      return null;
    }

    const members = await getOrganizationMembers(id);

    return {
      ...organization,
      memberCount: members.length,
      members,
    };
  } catch (error) {
    console.error("Error fetching organization details:", error);
    return null;
  }
}

function OrganizationTypeBadge({ type }: { type: string }) {
  const variants = {
    yacht_company: "default",
    cert_provider: "secondary",
  } as const;

  const labels = {
    yacht_company: "Yacht Company",
    cert_provider: "Certification Provider",
  } as const;

  return (
    <Badge variant={variants[type as keyof typeof variants] || "outline"}>
      {labels[type as keyof typeof labels] || type}
    </Badge>
  );
}

function OrganizationStatusBadge({ status }: { status: string }) {
  const variants = {
    pending: "destructive",
    verified: "default",
    suspended: "outline",
  } as const;

  const labels = {
    pending: "Pending",
    verified: "Verified",
    suspended: "Suspended",
  } as const;

  return (
    <Badge variant={variants[status as keyof typeof variants] || "outline"}>
      {labels[status as keyof typeof labels] || status}
    </Badge>
  );
}

function formatDate(date: Date | null) {
  if (!date) return "Not set";
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date));
}

async function OrganizationDetailsContent({ id }: { id: string }) {
  const organizationData = await getOrganizationDetails(id);

  if (!organizationData) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold text-gray-900">Organization Not Found</h2>
        <p className="text-gray-600 mt-2">
          The organization you're looking for doesn't exist or has been removed.
        </p>
        <Link href="/admin/organizations">
          <Button className="mt-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Organizations
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/organizations">
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {organizationData.name}
            </h1>
            <div className="flex items-center space-x-2 mt-1">
              <OrganizationTypeBadge type={organizationData.type} />
              <OrganizationStatusBadge status={organizationData.status} />
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          {organizationData.status === "pending" && (
            <Button>Verify Organization</Button>
          )}
          {organizationData.status === "verified" && (
            <Button variant="destructive">Suspend Organization</Button>
          )}
          {organizationData.status === "suspended" && (
            <Button variant="outline">Reactivate Organization</Button>
          )}
          <Button variant="outline">Edit Organization</Button>
        </div>
      </div>

      {/* Organization Details */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="w-5 h-5 mr-2" />
              Organization Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Contact Email</label>
              <div className="flex items-center mt-1">
                <Mail className="w-4 h-4 mr-2 text-gray-400" />
                <span>{organizationData.contactEmail}</span>
              </div>
            </div>
            
            {organizationData.website && (
              <div>
                <label className="text-sm font-medium text-gray-500">Website</label>
                <div className="mt-1">
                  <a
                    href={organizationData.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {organizationData.website}
                  </a>
                </div>
              </div>
            )}

            {organizationData.description && (
              <div>
                <label className="text-sm font-medium text-gray-500">Description</label>
                <p className="mt-1 text-gray-900">{organizationData.description}</p>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-gray-500">Created</label>
              <div className="flex items-center mt-1">
                <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                <span>{formatDate(organizationData.createdAt)}</span>
              </div>
            </div>

            {organizationData.verifiedAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">Verified</label>
                <div className="flex items-center mt-1">
                  <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                  <span>{formatDate(organizationData.verifiedAt)}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Members ({organizationData.memberCount})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {organizationData.members.length > 0 ? (
              <div className="space-y-3">
                {organizationData.members.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{member.name}</div>
                      <div className="text-sm text-gray-500">{member.email}</div>
                    </div>
                    <Badge variant="outline">{member.membershipRole}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No members found</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default async function OrganizationDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const session = await getServerSession();

  // Check authentication and authorization
  if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  const { id } = await params;

  return (
    <div className="space-y-6">
      <Suspense
        fallback={
          <div className="space-y-6">
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
            <div className="grid gap-6 md:grid-cols-2">
              <div className="h-64 bg-gray-200 rounded animate-pulse" />
              <div className="h-64 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        }
      >
        <OrganizationDetailsContent id={id} />
      </Suspense>
    </div>
  );
}
