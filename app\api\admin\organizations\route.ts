import { UserRole } from "@/lib/auth-config"
import {
  getOrganizationById,
  getOrganizations,
  updateOrganization,
  verifyOrganization
} from "@/lib/db"
import { getServerSession } from "@/lib/session"
import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"

// Validation schemas
const verifyOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  action: z.literal("verify")
})

const suspendOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  action: z.literal("suspend"),
  reason: z.string().optional()
})

const reactivateOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  action: z.literal("reactivate")
})

const updateOrganizationSchema = z.object({
  organizationId: z.string().min(1, "Organization ID is required"),
  action: z.literal("update"),
  updates: z.object({
    status: z.enum(["pending", "verified", "suspended"]).optional(),
    name: z.string().min(1).optional(),
    description: z.string().nullable().optional(),
    website: z.string().nullable().optional(),
  })
})

const actionSchema = z.discriminatedUnion("action", [
  verifyOrganizationSchema,
  suspendOrganizationSchema,
  reactivateOrganizationSchema,
  updateOrganizationSchema
])

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get("type") as "yacht_company" | "cert_provider" | undefined
    const status = searchParams.get("status") as "pending" | "verified" | "suspended" | undefined
    const limit = searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 50
    const offset = searchParams.get("offset") ? parseInt(searchParams.get("offset")!) : 0

    const organizations = await getOrganizations({
      type,
      status,
      limit,
      offset
    })

    return NextResponse.json({
      success: true,
      organizations,
      pagination: {
        limit,
        offset,
        hasMore: organizations.length === limit
      }
    })

  } catch (error) {
    console.error("Error fetching organizations:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()

    // Check authentication and authorization
    if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = actionSchema.parse(body)

    switch (validatedData.action) {
      case "verify": {
        // Verify the organization exists
        const organization = await getOrganizationById(validatedData.organizationId)
        if (!organization) {
          return NextResponse.json(
            { error: "Organization not found" },
            { status: 404 }
          )
        }

        if (organization.status === "verified") {
          return NextResponse.json(
            { error: "Organization is already verified" },
            { status: 400 }
          )
        }

        // Verify the organization
        await verifyOrganization(validatedData.organizationId, session.userId)

        return NextResponse.json({
          success: true,
          message: "Organization verified successfully"
        })
      }

      case "suspend": {
        // Verify the organization exists
        const organization = await getOrganizationById(validatedData.organizationId)
        if (!organization) {
          return NextResponse.json(
            { error: "Organization not found" },
            { status: 404 }
          )
        }

        if (organization.status === "suspended") {
          return NextResponse.json(
            { error: "Organization is already suspended" },
            { status: 400 }
          )
        }

        // Import suspend function dynamically to avoid import issues
        const { suspendOrganization } = await import("@/lib/db")
        await suspendOrganization(validatedData.organizationId, session.userId, validatedData.reason)

        return NextResponse.json({
          success: true,
          message: "Organization suspended successfully"
        })
      }

      case "reactivate": {
        // Verify the organization exists
        const organization = await getOrganizationById(validatedData.organizationId)
        if (!organization) {
          return NextResponse.json(
            { error: "Organization not found" },
            { status: 404 }
          )
        }

        if (organization.status !== "suspended") {
          return NextResponse.json(
            { error: "Organization is not suspended" },
            { status: 400 }
          )
        }

        // Import reactivate function dynamically to avoid import issues
        const { reactivateOrganization } = await import("@/lib/db")
        await reactivateOrganization(validatedData.organizationId, session.userId)

        return NextResponse.json({
          success: true,
          message: "Organization reactivated successfully"
        })
      }

      case "update": {
        // Verify the organization exists
        const organization = await getOrganizationById(validatedData.organizationId)
        if (!organization) {
          return NextResponse.json(
            { error: "Organization not found" },
            { status: 404 }
          )
        }

        // Update the organization
        await updateOrganization(validatedData.organizationId, validatedData.updates)

        return NextResponse.json({
          success: true,
          message: "Organization updated successfully"
        })
      }

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        )
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: "Validation error",
          details: error.errors
        },
        { status: 400 }
      )
    }

    console.error("Error processing organization action:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
