import { OrganizationActions } from "@/components/admin/organization-actions";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-config";
import { getServerSession } from "@/lib/session";
import { redirect } from "next/navigation";
import { Suspense } from "react";

async function getOrganizations(limit = 50) {
  try {
    // Import the function dynamically to avoid import issues
    const { getAllOrganizations } = await import("@/lib/db");

    // Get real organizations from database
    return await getAllOrganizations(limit);
  } catch (error) {
    console.error("Error fetching organizations:", error);
    return [];
  }
}

async function getOrganizationStats() {
  try {
    // Import the function dynamically to avoid import issues
    const { getAdminOrganizationStats } = await import("@/lib/db");

    // Get real organization statistics from database
    return await getAdminOrganizationStats();
  } catch (error) {
    console.error("Error fetching organization stats:", error);
    return {
      total: 0,
      pending: 0,
      verified: 0,
      recent: 0,
    };
  }
}

function OrganizationTypeBadge({ type }: { type: string }) {
  const variants = {
    yacht_company: "default",
    cert_provider: "secondary",
  } as const;

  const labels = {
    yacht_company: "Yacht Company",
    cert_provider: "Certification Provider",
  } as const;

  return (
    <Badge variant={variants[type as keyof typeof variants] || "outline"}>
      {labels[type as keyof typeof labels] || type}
    </Badge>
  );
}

function OrganizationStatusBadge({ status }: { status: string }) {
  const variants = {
    pending: "destructive",
    verified: "default",
    suspended: "outline",
  } as const;

  const labels = {
    pending: "Pending",
    verified: "Verified",
    suspended: "Suspended",
  } as const;

  return (
    <Badge variant={variants[status as keyof typeof variants] || "outline"}>
      {labels[status as keyof typeof labels] || status}
    </Badge>
  );
}

function formatDate(date: Date | null) {
  if (!date) return "Not set";
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(new Date(date));
}

async function OrganizationStatsCards() {
  const stats = await getOrganizationStats();

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Total Organizations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
          <p className="text-xs text-muted-foreground">All registered</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Pending Verification
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.pending}</div>
          <p className="text-xs text-muted-foreground">Awaiting review</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Verified</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.verified}</div>
          <p className="text-xs text-muted-foreground">Active organizations</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Recent Organizations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.recent}</div>
          <p className="text-xs text-muted-foreground">Last 7 days</p>
        </CardContent>
      </Card>
    </>
  );
}

async function OrganizationsTableContent() {
  const orgsData = await getOrganizations();

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-gray-50">
                <th className="text-left p-4 font-medium">Organization</th>
                <th className="text-left p-4 font-medium">Type</th>
                <th className="text-left p-4 font-medium">Status</th>
                <th className="text-left p-4 font-medium">Members</th>
                <th className="text-left p-4 font-medium">Contact</th>
                <th className="text-left p-4 font-medium">Created</th>
                <th className="text-left p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {orgsData.map((org) => (
                <tr key={org.id} className="border-b hover:bg-gray-50">
                  <td className="p-4">
                    <div>
                      <div className="font-medium">{org.name}</div>
                      {org.description && (
                        <div className="text-sm text-gray-500 truncate max-w-xs">
                          {org.description}
                        </div>
                      )}
                      {org.website && (
                        <a
                          href={org.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {org.website}
                        </a>
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <OrganizationTypeBadge type={org.type} />
                  </td>
                  <td className="p-4">
                    <div className="space-y-1">
                      <OrganizationStatusBadge status={org.status} />
                      {org.status === "verified" && org.verifierName && (
                        <div className="text-xs text-gray-500">
                          by {org.verifierName}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="text-sm">
                      <span className="font-medium">{org.memberCount}</span>
                      <span className="text-gray-500 ml-1">
                        {org.memberCount === 1 ? "member" : "members"}
                      </span>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="text-sm text-gray-600">
                      {org.contactEmail}
                    </div>
                  </td>
                  <td className="p-4 text-sm text-gray-600">
                    {formatDate(org.createdAt)}
                  </td>
                  <td className="p-4">
                    <OrganizationActions
                      organizationId={org.id}
                      organizationName={org.name}
                      status={org.status}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {orgsData.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No organizations found
        </div>
      )}
    </div>
  );
}

export default async function OrganizationsManagement() {
  const session = await getServerSession();

  // Check authentication and authorization
  if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Organization Management
          </h1>
          <p className="text-muted-foreground">
            Review and manage organization accounts
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">Export Organizations</Button>
          <Button>Create Organization</Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Suspense
          fallback={
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Organizations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Loading...</div>
                  <p className="text-xs text-muted-foreground">
                    All registered
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Pending Verification
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Loading...</div>
                  <p className="text-xs text-muted-foreground">
                    Awaiting review
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Verified
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Loading...</div>
                  <p className="text-xs text-muted-foreground">
                    Active organizations
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Recent Organizations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">Loading...</div>
                  <p className="text-xs text-muted-foreground">Last 7 days</p>
                </CardContent>
              </Card>
            </>
          }
        >
          <OrganizationStatsCards />
        </Suspense>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Organizations</CardTitle>
          <CardDescription>
            Complete list of organization accounts in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-64 bg-gray-200 rounded animate-pulse" />
              </div>
            }
          >
            <OrganizationsTableContent />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
