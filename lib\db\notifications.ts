/**
 * Notification Management Functions
 *
 * This module handles all notification-related database operations including:
 * - Notification CRUD operations
 * - Certificate expiry notification generation
 * - Notification queries and filtering
 * - Notification preferences and settings
 */

import { neon } from "@neondatabase/serverless"
import { and, desc, eq, gte, lte, sql } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import { nanoid } from "nanoid"
import {
  certificates,
  notifications
} from "./schema"

// Initialize database connection for this module
const neonSql = neon(process.env.DATABASE_URL!)
const db = drizzle(neonSql)

// ============================================================================
// NOTIFICATION TYPES AND INTERFACES
// ============================================================================

export type NotificationType = 'certificate_expiry' | 'system' | 'reminder'

export interface NotificationData {
  id?: string
  userId: string
  type: NotificationType
  title: string
  message: string
  read?: boolean
  actionUrl?: string
  metadata?: any
  expiresAt?: Date
}

export interface ExpiryNotificationMetadata {
  certificateId: string
  certificateName: string
  expiryDate: string
  daysUntilExpiry: number
}

// ============================================================================
// BASIC NOTIFICATION OPERATIONS
// ============================================================================

/**
 * Create a new notification
 */
export async function createNotification(data: NotificationData) {
  try {
    const id = data.id || nanoid()

    const notification = await db.insert(notifications).values({
      id,
      userId: data.userId,
      type: data.type,
      title: data.title,
      message: data.message,
      read: data.read || false,
      actionUrl: data.actionUrl,
      metadata: data.metadata ? JSON.stringify(data.metadata) : null,
      expiresAt: data.expiresAt,
    }).returning()

    return {
      success: true,
      notification: notification[0]
    }
  } catch (error) {
    console.error("Error creating notification:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

/**
 * Get all notifications for a user
 */
export async function getNotificationsByUserId(userId: string, includeRead: boolean = true) {
  try {
    const conditions = [eq(notifications.userId, userId)]

    if (!includeRead) {
      conditions.push(eq(notifications.read, false))
    }

    const userNotifications = await db
      .select()
      .from(notifications)
      .where(and(...conditions))
      .orderBy(desc(notifications.createdAt))

    return userNotifications.map(notification => ({
      ...notification,
      metadata: notification.metadata ? JSON.parse(notification.metadata) : null
    }))
  } catch (error) {
    console.error("Error getting notifications:", error)
    return []
  }
}

/**
 * Get unread notifications count for a user
 */
export async function getUnreadNotificationCount(userId: string) {
  try {
    const result = await db
      .select({ count: sql`count(*)` })
      .from(notifications)
      .where(
        and(
          eq(notifications.userId, userId),
          eq(notifications.read, false)
        )
      )

    return parseInt(result[0]?.count as string) || 0
  } catch (error) {
    console.error("Error getting unread notification count:", error)
    return 0
  }
}

/**
 * Mark notification as read
 */
export async function markNotificationAsRead(notificationId: string, userId: string) {
  try {
    const result = await db
      .update(notifications)
      .set({ read: true })
      .where(
        and(
          eq(notifications.id, notificationId),
          eq(notifications.userId, userId)
        )
      )
      .returning()

    return {
      success: result.length > 0,
      notification: result[0] || null
    }
  } catch (error) {
    console.error("Error marking notification as read:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(userId: string) {
  try {
    const result = await db
      .update(notifications)
      .set({ read: true })
      .where(
        and(
          eq(notifications.userId, userId),
          eq(notifications.read, false)
        )
      )

    return {
      success: true,
      updatedCount: result.rowCount || 0
    }
  } catch (error) {
    console.error("Error marking all notifications as read:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

/**
 * Delete a notification
 */
export async function deleteNotification(notificationId: string, userId: string) {
  try {
    const result = await db
      .delete(notifications)
      .where(
        and(
          eq(notifications.id, notificationId),
          eq(notifications.userId, userId)
        )
      )

    return {
      success: result.rowCount > 0,
      deletedCount: result.rowCount || 0
    }
  } catch (error) {
    console.error("Error deleting notification:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

// ============================================================================
// CERTIFICATE EXPIRY NOTIFICATIONS
// ============================================================================

/**
 * Generate certificate expiry notifications for a user
 */
export async function generateExpiryNotifications(userId: string, daysThresholds: number[] = [90, 60, 30, 7]) {
  try {
    const today = new Date()
    const notifications: NotificationData[] = []

    for (const days of daysThresholds) {
      const targetDate = new Date()
      targetDate.setDate(today.getDate() + days)

      // Get certificates expiring on this specific day
      const expiringCerts = await db
        .select()
        .from(certificates)
        .where(
          and(
            eq(certificates.userId, userId),
            gte(certificates.expiryDate!, targetDate),
            lte(certificates.expiryDate!, new Date(targetDate.getTime() + 24 * 60 * 60 * 1000))
          )
        )

      for (const cert of expiringCerts) {
        // Check if we already have a notification for this certificate and threshold
        const existingNotification = await db
          .select()
          .from(notifications)
          .where(
            and(
              eq(notifications.userId, userId),
              eq(notifications.type, 'certificate_expiry'),
              sql`JSON_EXTRACT(metadata, '$.certificateId') = ${cert.id}`,
              sql`JSON_EXTRACT(metadata, '$.daysUntilExpiry') = ${days}`
            )
          )
          .limit(1)

        if (existingNotification.length === 0) {
          const metadata: ExpiryNotificationMetadata = {
            certificateId: cert.id,
            certificateName: cert.name,
            expiryDate: cert.expiryDate!.toISOString(),
            daysUntilExpiry: days
          }

          notifications.push({
            userId,
            type: 'certificate_expiry',
            title: `Certificate Expiring ${days === 0 ? 'Today' : `in ${days} days`}`,
            message: `Your ${cert.name} certificate will expire ${days === 0 ? 'today' : `in ${days} days`} on ${cert.expiryDate!.toLocaleDateString()}.`,
            actionUrl: `/certificates`,
            metadata
          })
        }
      }
    }

    // Create all notifications
    const results = []
    for (const notificationData of notifications) {
      const result = await createNotification(notificationData)
      results.push(result)
    }

    return {
      success: true,
      created: results.filter(r => r.success).length,
      total: notifications.length
    }
  } catch (error) {
    console.error("Error generating expiry notifications:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

/**
 * Clean up expired notifications
 */
export async function cleanupExpiredNotifications() {
  try {
    const now = new Date()

    const result = await db
      .delete(notifications)
      .where(
        and(
          lte(notifications.expiresAt!, now)
        )
      )

    return {
      success: true,
      deletedCount: result.rowCount || 0
    }
  } catch (error) {
    console.error("Error cleaning up expired notifications:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}
