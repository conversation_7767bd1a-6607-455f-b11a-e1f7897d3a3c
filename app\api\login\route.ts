import { AuthenticationError, ValidationError, handleApiError } from "@/lib/api-error-handler"
import { getUserByEmail } from "@/lib/db"
import { loginSchema, validateRequestBody } from "@/lib/validation-schemas"
import { compare } from "bcrypt"
import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    // Validate request body
    const validatedData = await validateRequestBody(request, loginSchema);
    const { email, password } = validatedData;

    // Get user from database
    const user = await getUserByEmail(email)

    if (!user) {
      throw new AuthenticationError("Invalid credentials");
    }

    // Check if user has a password (not OAuth-only)
    if (!user.password) {
      throw new AuthenticationError("This account was created with social login. Please sign in with Google or add a password in your profile.");
    }

    // Verify password
    const isPasswordValid = await compare(password, user.password)

    if (!isPasswordValid) {
      throw new AuthenticationError("Invalid credentials");
    }

    // Check email verification status
    if (!user.emailVerified) {
      return NextResponse.json({
        error: "Please verify your email address before signing in.",
        requiresVerification: true,
        userEmail: user.email
      }, { status: 403 })
    }

    // Create session cookie with user information including role and subscription plan
    const cookieStore = await cookies()
    cookieStore.set({
      name: "session",
      value: JSON.stringify({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role || "individual_user", // Default to INDIVIDUAL_USER for existing users
          subscriptionPlan: user.subscriptionPlan || "individual_free", // Default plan
          emailVerified: user.emailVerified, // Include email verification status
          tenantId: user.tenantId,
          tenantRole: user.tenantRole
        }
      }),
      httpOnly: true,
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 1 week
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax"
    })

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name
      }
    })
  } catch (error) {
    return handleApiError(error, 'login');
  }
}
