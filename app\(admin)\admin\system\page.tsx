import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-config";
import { getServerSession } from "@/lib/session";
import { redirect } from "next/navigation";
import { Suspense } from "react";

async function getSystemHealth() {
  try {
    // Import the function dynamically to avoid import issues
    const { getSystemHealthData } = await import("@/lib/db");

    // Get real system health data from database
    return await getSystemHealthData();
  } catch (error) {
    console.error("Error fetching system health:", error);
    return {
      database: { healthy: false, latency: 0 },
      storage: { healthy: false, usage: "Unknown" },
      email: { healthy: false, lastSent: null },
      auth: { healthy: false, activeSessions: 0 },
      recentJobs: [],
      recentAudits: [],
    };
  }
}

function HealthIndicator({
  healthy,
  label,
}: {
  healthy: boolean;
  label: string;
}) {
  return (
    <div className="flex items-center justify-between">
      <span className="text-sm">{label}</span>
      <Badge variant={healthy ? "default" : "destructive"}>
        {healthy ? "Healthy" : "Error"}
      </Badge>
    </div>
  );
}

function formatDate(date: Date | string | null) {
  if (!date) return "Never";
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date));
}

async function SystemHealthContent() {
  const health = await getSystemHealth();

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database</CardTitle>
            <Badge
              variant={health.database.healthy ? "default" : "destructive"}
            >
              {health.database.healthy ? "Online" : "Offline"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {health.database.latency}ms
            </div>
            <p className="text-xs text-muted-foreground">Average latency</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Storage</CardTitle>
            <Badge variant={health.storage.healthy ? "default" : "destructive"}>
              {health.storage.healthy ? "Available" : "Error"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{health.storage.usage}</div>
            <p className="text-xs text-muted-foreground">Storage used</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Email Service</CardTitle>
            <Badge variant={health.email.healthy ? "default" : "destructive"}>
              {health.email.healthy ? "Active" : "Down"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">✓</div>
            <p className="text-xs text-muted-foreground">Operational</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Authentication
            </CardTitle>
            <Badge variant={health.auth.healthy ? "default" : "destructive"}>
              {health.auth.healthy ? "Secure" : "Error"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {health.auth.activeSessions}
            </div>
            <p className="text-xs text-muted-foreground">Active sessions</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>System Components</CardTitle>
            <CardDescription>
              Health status of core system components
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <HealthIndicator
              healthy={health.database.healthy}
              label="PostgreSQL Database"
            />
            <HealthIndicator
              healthy={health.storage.healthy}
              label="UploadThing Storage"
            />
            <HealthIndicator
              healthy={health.email.healthy}
              label="Resend Email Service"
            />
            <HealthIndicator healthy={health.auth.healthy} label="Stack Auth" />
            <HealthIndicator healthy={true} label="Rate Limiting" />
            <HealthIndicator healthy={true} label="CSRF Protection" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Actions</CardTitle>
            <CardDescription>
              Administrative tools and maintenance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button variant="outline" className="w-full justify-start">
              Run Database Cleanup
            </Button>
            <Button variant="outline" className="w-full justify-start">
              Clear Cache
            </Button>
            <Button variant="outline" className="w-full justify-start">
              Test Email Service
            </Button>
            <Button variant="outline" className="w-full justify-start">
              Export System Logs
            </Button>
            <Button variant="outline" className="w-full justify-start">
              Generate Health Report
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Cleanup Jobs</CardTitle>
            <CardDescription>Automated maintenance tasks</CardDescription>
          </CardHeader>
          <CardContent>
            {health.recentJobs.length > 0 ? (
              <div className="space-y-3">
                {health.recentJobs.map((job) => (
                  <div
                    key={job.id}
                    className="flex items-center justify-between p-3 border rounded"
                  >
                    <div>
                      <div className="font-medium text-sm">{job.jobType}</div>
                      <div className="text-xs text-gray-500">
                        {formatDate(job.createdAt)}
                      </div>
                    </div>
                    <Badge
                      variant={
                        job.status === "completed"
                          ? "default"
                          : job.status === "failed"
                          ? "destructive"
                          : job.status === "running"
                          ? "secondary"
                          : "outline"
                      }
                    >
                      {job.status}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 text-sm">
                No recent cleanup jobs
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Audit Logs</CardTitle>
            <CardDescription>
              System security and compliance events
            </CardDescription>
          </CardHeader>
          <CardContent>
            {health.recentAudits.length > 0 ? (
              <div className="space-y-3">
                {health.recentAudits.map((audit) => (
                  <div
                    key={audit.id}
                    className="flex items-center justify-between p-3 border rounded"
                  >
                    <div>
                      <div className="font-medium text-sm">
                        {audit.deletionType}
                      </div>
                      <div className="text-xs text-gray-500">
                        {audit.userEmail} - {formatDate(audit.createdAt)}
                      </div>
                    </div>
                    <Badge variant="outline">{audit.initiatedBy}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 text-sm">
                No recent audit events
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default async function SystemMonitoring() {
  const session = await getServerSession();

  // Check authentication and authorization
  if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            System Monitoring
          </h1>
          <p className="text-muted-foreground">
            Monitor system health and performance
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">Download Report</Button>
          <Button>Refresh Status</Button>
        </div>
      </div>

      <Suspense
        fallback={
          <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader className="space-y-0 pb-2">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </CardHeader>
                  <CardContent>
                    <div className="h-8 bg-gray-200 rounded animate-pulse mb-2" />
                    <div className="h-3 bg-gray-200 rounded animate-pulse" />
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="grid gap-6 md:grid-cols-2">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i}>
                  <CardHeader>
                    <div className="h-6 bg-gray-200 rounded animate-pulse" />
                  </CardHeader>
                  <CardContent>
                    <div className="h-32 bg-gray-200 rounded animate-pulse" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        }
      >
        <SystemHealthContent />
      </Suspense>
    </div>
  );
}
