import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-config";
import { getServerSession } from "@/lib/session";
import { redirect } from "next/navigation";
import { Suspense } from "react";

async function getUsers(limit = 50) {
  try {
    // Return mock data for now to avoid build issues during initial implementation
    return [
      {
        id: "user1",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "individual_user",
        subscriptionPlan: "individual_free",
        emailVerified: true,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        organizations: [],
      },
      {
        id: "user2",
        name: "<PERSON>",
        email: "<EMAIL>",
        role: "yacht_company",
        subscriptionPlan: "company_basic",
        emailVerified: true,
        lastLoginAt: new Date(),
        createdAt: new Date(),
        organizations: [
          {
            organizationName: "Elite Yacht Co",
            organizationType: "yacht_company",
            membershipRole: "owner",
          },
        ],
      },
    ];
  } catch (error) {
    console.error("Error fetching users:", error);
    return [];
  }
}

function UserRoleBadge({ role }: { role: string }) {
  const variants = {
    individual_user: "default",
    cert_provider: "secondary",
    yacht_company: "outline",
    system_admin: "destructive",
  } as const;

  const labels = {
    individual_user: "Individual",
    cert_provider: "Cert Provider",
    yacht_company: "Yacht Company",
    system_admin: "System Admin",
  } as const;

  return (
    <Badge variant={variants[role as keyof typeof variants] || "outline"}>
      {labels[role as keyof typeof labels] || role}
    </Badge>
  );
}

function formatDate(date: Date | null) {
  if (!date) return "Never";
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date));
}

async function UsersTableContent() {
  const usersData = await getUsers();

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-gray-50">
                <th className="text-left p-4 font-medium">User</th>
                <th className="text-left p-4 font-medium">Role</th>
                <th className="text-left p-4 font-medium">Organizations</th>
                <th className="text-left p-4 font-medium">Status</th>
                <th className="text-left p-4 font-medium">Last Login</th>
                <th className="text-left p-4 font-medium">Joined</th>
                <th className="text-left p-4 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {usersData.map((user) => (
                <tr key={user.id} className="border-b hover:bg-gray-50">
                  <td className="p-4">
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                    </div>
                  </td>
                  <td className="p-4">
                    <UserRoleBadge role={user.role} />
                  </td>
                  <td className="p-4">
                    {user.organizations.length > 0 ? (
                      <div className="space-y-1">
                        {user.organizations.map((org, index) => (
                          <div key={index} className="text-sm">
                            <span className="font-medium">
                              {org.organizationName}
                            </span>
                            <span className="text-gray-500 ml-1">
                              ({org.membershipRole})
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <span className="text-gray-400 text-sm">None</span>
                    )}
                  </td>
                  <td className="p-4">
                    <div className="space-y-1">
                      <Badge
                        variant={user.emailVerified ? "default" : "destructive"}
                      >
                        {user.emailVerified ? "Verified" : "Unverified"}
                      </Badge>
                      <div className="text-xs text-gray-500">
                        {user.subscriptionPlan}
                      </div>
                    </div>
                  </td>
                  <td className="p-4 text-sm text-gray-600">
                    {formatDate(user.lastLoginAt)}
                  </td>
                  <td className="p-4 text-sm text-gray-600">
                    {formatDate(user.createdAt)}
                  </td>
                  <td className="p-4">
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {usersData.length === 0 && (
        <div className="text-center py-8 text-gray-500">No users found</div>
      )}
    </div>
  );
}

export default async function UsersManagement() {
  const session = await getServerSession();

  // Check authentication and authorization
  if (!session || session.role !== UserRole.SYSTEM_ADMIN) {
    redirect("/dashboard?error=unauthorized");
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts and permissions
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">Export Users</Button>
          <Button>Create User</Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Loading...</div>
            <p className="text-xs text-muted-foreground">All active accounts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Individual Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Loading...</div>
            <p className="text-xs text-muted-foreground">
              Maritime professionals
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Business Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Loading...</div>
            <p className="text-xs text-muted-foreground">
              Organization members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unverified</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Loading...</div>
            <p className="text-xs text-muted-foreground">
              Pending verification
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
          <CardDescription>
            Complete list of user accounts in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense
            fallback={
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded animate-pulse" />
                <div className="h-64 bg-gray-200 rounded animate-pulse" />
              </div>
            }
          >
            <UsersTableContent />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
