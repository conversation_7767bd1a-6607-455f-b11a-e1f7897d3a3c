/**
 * Organization Lifecycle Management
 *
 * This module handles organization lifecycle events including:
 * - Admin account deletion and grace periods
 * - Organization recovery requests
 * - Cleanup job scheduling
 * - Grace period management
 */

import { neon } from "@neondatabase/serverless"
import { and, desc, eq } from "drizzle-orm"
import { drizzle } from "drizzle-orm/neon-http"
import { nanoid } from "nanoid"
import {
  adminRecoveryRequests,
  cleanupJobs,
  organizationLifecycleEvents
} from "./schema"

// Initialize database connection for this module
const sql = neon(process.env.DATABASE_URL!)
const db = drizzle(sql)

// ============================================================================
// LIFECYCLE EVENT MANAGEMENT
// ============================================================================

/**
 * Create a lifecycle event for an organization
 */
export async function createOrganizationLifecycleEvent(eventData: {
  organizationId: string
  eventType: "admin_deleted" | "grace_period_started" | "grace_period_ended" | "admin_recovered"
  triggeredBy: string
  gracePeriodEnds?: Date
  metadata?: any
}) {
  try {
    const eventId = nanoid()
    await db.insert(organizationLifecycleEvents).values({
      id: eventId,
      organizationId: eventData.organizationId,
      eventType: eventData.eventType,
      triggeredBy: eventData.triggeredBy,
      gracePeriodEnds: eventData.gracePeriodEnds,
      metadata: eventData.metadata ? JSON.stringify(eventData.metadata) : null,
    })
    return { success: true, eventId }
  } catch (error) {
    console.error("Error creating organization lifecycle event:", error)
    throw error
  }
}

/**
 * Get lifecycle events for an organization
 */
export async function getOrganizationLifecycleEvents(organizationId: string) {
  try {
    const events = await db
      .select()
      .from(organizationLifecycleEvents)
      .where(eq(organizationLifecycleEvents.organizationId, organizationId))
      .orderBy(desc(organizationLifecycleEvents.triggeredAt))

    return events.map(event => ({
      ...event,
      metadata: event.metadata ? JSON.parse(event.metadata) : null
    }))
  } catch (error) {
    console.error("Error getting organization lifecycle events:", error)
    return []
  }
}

/**
 * Get the latest grace period event for an organization
 */
export async function getLatestGracePeriodEvent(organizationId: string) {
  try {
    const result = await db
      .select()
      .from(organizationLifecycleEvents)
      .where(
        and(
          eq(organizationLifecycleEvents.organizationId, organizationId),
          eq(organizationLifecycleEvents.eventType, "grace_period_started")
        )
      )
      .orderBy(desc(organizationLifecycleEvents.triggeredAt))
      .limit(1)

    if (result.length === 0) return null

    const event = result[0]
    return {
      ...event,
      metadata: event.metadata ? JSON.parse(event.metadata) : null
    }
  } catch (error) {
    console.error("Error getting latest grace period event:", error)
    return null
  }
}

// ============================================================================
// GRACE PERIOD MANAGEMENT
// ============================================================================

/**
 * Handle admin account deletion - create grace period event
 */
export async function handleAdminAccountDeletion(organizationId: string, deletedAdminUserId: string) {
  try {
    const gracePeriodEnds = new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)) // 30 days

    const result = await createOrganizationLifecycleEvent({
      organizationId,
      eventType: "grace_period_started",
      triggeredBy: deletedAdminUserId,
      gracePeriodEnds,
      metadata: {
        deletedAdminUserId,
        gracePeriodDays: 30
      }
    })

    // Schedule cleanup job for when grace period expires
    await scheduleOrganizationCleanup(organizationId, gracePeriodEnds)

    return result
  } catch (error) {
    console.error("Error handling admin account deletion:", error)
    throw error
  }
}

/**
 * Check if organization is in grace period
 */
export async function isOrganizationInGracePeriod(organizationId: string) {
  try {
    const latestEvent = await getLatestGracePeriodEvent(organizationId)
    if (!latestEvent || !latestEvent.gracePeriodEnds) {
      return false
    }

    return new Date() < latestEvent.gracePeriodEnds
  } catch (error) {
    console.error("Error checking organization grace period:", error)
    return false
  }
}

/**
 * Get grace period days remaining
 */
export async function getOrganizationGracePeriodDaysRemaining(organizationId: string) {
  try {
    const latestEvent = await getLatestGracePeriodEvent(organizationId)
    if (!latestEvent || !latestEvent.gracePeriodEnds) {
      return 0
    }

    const now = new Date()
    if (now >= latestEvent.gracePeriodEnds) {
      return 0
    }

    const msRemaining = latestEvent.gracePeriodEnds.getTime() - now.getTime()
    const daysRemaining = Math.ceil(msRemaining / (24 * 60 * 60 * 1000))

    return Math.max(0, daysRemaining)
  } catch (error) {
    console.error("Error getting grace period days remaining:", error)
    return 0
  }
}

// ============================================================================
// CLEANUP JOB MANAGEMENT
// ============================================================================

/**
 * Schedule organization cleanup job
 */
export async function scheduleOrganizationCleanup(organizationId: string, scheduledFor: Date) {
  try {
    const jobId = nanoid()
    await db.insert(cleanupJobs).values({
      id: jobId,
      jobType: "organization_grace_period_cleanup",
      status: "pending",
      scheduledFor,
      metadata: JSON.stringify({ organizationId }),
    })
    return { success: true, jobId }
  } catch (error) {
    console.error("Error scheduling organization cleanup:", error)
    throw error
  }
}

// ============================================================================
// ADMIN RECOVERY REQUESTS
// ============================================================================

/**
 * Create an admin recovery request
 */
export async function createAdminRecoveryRequest(requestData: {
  organizationId: string
  requestedBy: string
  requestReason: string
  expiresAt?: Date
}) {
  try {
    const requestId = nanoid()
    const expiresAt = requestData.expiresAt || new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)) // 7 days default

    await db.insert(adminRecoveryRequests).values({
      id: requestId,
      organizationId: requestData.organizationId,
      requestedBy: requestData.requestedBy,
      requestReason: requestData.requestReason,
      expiresAt,
    })

    return { success: true, requestId }
  } catch (error) {
    console.error("Error creating admin recovery request:", error)
    throw error
  }
}

/**
 * Get admin recovery requests for review
 */
export async function getAdminRecoveryRequests(status?: string) {
  try {
    let query = db.select().from(adminRecoveryRequests)

    if (status) {
      query = query.where(eq(adminRecoveryRequests.status, status))
    }

    const requests = await query.orderBy(desc(adminRecoveryRequests.createdAt))
    return requests
  } catch (error) {
    console.error("Error getting admin recovery requests:", error)
    return []
  }
}

/**
 * Review an admin recovery request
 */
export async function reviewAdminRecoveryRequest(
  requestId: string,
  reviewedBy: string,
  status: "approved" | "rejected",
  reviewNotes?: string
) {
  try {
    await db
      .update(adminRecoveryRequests)
      .set({
        status,
        reviewedBy,
        reviewedAt: new Date(),
        reviewNotes,
        updatedAt: new Date(),
      })
      .where(eq(adminRecoveryRequests.id, requestId))

    return { success: true }
  } catch (error) {
    console.error("Error reviewing admin recovery request:", error)
    throw error
  }
}
