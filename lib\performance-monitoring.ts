import { logger } from "@/lib/logger";
import { NextRequest, NextResponse } from "next/server";

// Performance metrics interface
export interface PerformanceMetrics {
  requestId: string;
  method: string;
  path: string;
  statusCode: number;
  duration: number;
  timestamp: string;
  userAgent?: string;
  ip?: string;
  userId?: string;
  memoryUsage?: NodeJS.MemoryUsage;
  dbQueries?: number;
  dbQueryTime?: number;
  cacheHits?: number;
  cacheMisses?: number;
  errorType?: string;
  errorMessage?: string;
}

// Performance thresholds for alerting
export const performanceThresholds = {
  slowRequest: 1000, // 1 second
  verySlowRequest: 5000, // 5 seconds
  highMemoryUsage: 100 * 1024 * 1024, // 100MB
  maxDbQueries: 10, // Maximum DB queries per request
  slowDbQuery: 500, // 500ms for DB queries
} as const;

// In-memory performance metrics store
class PerformanceStore {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics

  add(metric: PerformanceMetrics) {
    this.metrics.push(metric);

    // Keep only the last N metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log performance issues
    this.checkPerformanceThresholds(metric);
  }

  private checkPerformanceThresholds(metric: PerformanceMetrics) {
    const issues: string[] = [];

    if (metric.duration > performanceThresholds.verySlowRequest) {
      issues.push(`Very slow request: ${metric.duration}ms`);
    } else if (metric.duration > performanceThresholds.slowRequest) {
      issues.push(`Slow request: ${metric.duration}ms`);
    }

    if (metric.memoryUsage && metric.memoryUsage.heapUsed > performanceThresholds.highMemoryUsage) {
      issues.push(`High memory usage: ${Math.round(metric.memoryUsage.heapUsed / 1024 / 1024)}MB`);
    }

    if (metric.dbQueries && metric.dbQueries > performanceThresholds.maxDbQueries) {
      issues.push(`Too many DB queries: ${metric.dbQueries}`);
    }

    if (metric.dbQueryTime && metric.dbQueryTime > performanceThresholds.slowDbQuery) {
      issues.push(`Slow DB queries: ${metric.dbQueryTime}ms`);
    }

    if (issues.length > 0) {
      logger.warn("performance", "Performance issues detected", {
        requestId: metric.requestId,
        path: metric.path,
        issues,
        metric,
      });
    }
  }

  getMetrics(limit = 100): PerformanceMetrics[] {
    return this.metrics.slice(-limit);
  }

  getAverageResponseTime(minutes = 5): number {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter(m =>
      new Date(m.timestamp).getTime() > cutoff
    );

    if (recentMetrics.length === 0) return 0;

    const total = recentMetrics.reduce((sum, m) => sum + m.duration, 0);
    return Math.round(total / recentMetrics.length);
  }

  getErrorRate(minutes = 5): number {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    const recentMetrics = this.metrics.filter(m =>
      new Date(m.timestamp).getTime() > cutoff
    );

    if (recentMetrics.length === 0) return 0;

    const errors = recentMetrics.filter(m => m.statusCode >= 400).length;
    return Math.round((errors / recentMetrics.length) * 100);
  }

  getSlowRequestCount(minutes = 5): number {
    const cutoff = Date.now() - (minutes * 60 * 1000);
    return this.metrics.filter(m =>
      new Date(m.timestamp).getTime() > cutoff &&
      m.duration > performanceThresholds.slowRequest
    ).length;
  }
}

// Global performance store
const performanceStore = new PerformanceStore();

// Database query tracking
export class DatabaseQueryTracker {
  private queries: Array<{ query: string; duration: number; timestamp: number }> = [];
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  addQuery(query: string, duration: number) {
    this.queries.push({
      query: query.substring(0, 200), // Truncate long queries
      duration,
      timestamp: Date.now(),
    });
  }

  getMetrics() {
    const totalTime = this.queries.reduce((sum, q) => sum + q.duration, 0);
    return {
      count: this.queries.length,
      totalTime,
      averageTime: this.queries.length > 0 ? totalTime / this.queries.length : 0,
      slowQueries: this.queries.filter(q => q.duration > performanceThresholds.slowDbQuery),
    };
  }
}

// Performance monitoring middleware
export function createPerformanceMonitor() {
  return function performanceMonitor(
    handler: (req: NextRequest, context?: any) => Promise<NextResponse>
  ) {
    return async function monitoredHandler(req: NextRequest, context?: any): Promise<NextResponse> {
      const startTime = Date.now();
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const startMemory = process.memoryUsage();

      // Create database query tracker
      const dbTracker = new DatabaseQueryTracker();

      // Add tracker to request context if available
      if (context) {
        context.dbTracker = dbTracker;
      }

      let response: NextResponse;
      let error: Error | null = null;

      try {
        response = await handler(req, context);
      } catch (err) {
        error = err instanceof Error ? err : new Error(String(err));

        // Create error response
        response = NextResponse.json(
          { error: "Internal Server Error", requestId },
          { status: 500 }
        );
      }

      const endTime = Date.now();
      const duration = endTime - startTime;
      const endMemory = process.memoryUsage();
      const dbMetrics = dbTracker.getMetrics();

      // Extract request information
      const forwarded = req.headers.get("x-forwarded-for");
      const ip = forwarded ? forwarded.split(",")[0].trim() : "unknown";
      const userAgent = req.headers.get("user-agent") || undefined;

      // Create performance metric
      const metric: PerformanceMetrics = {
        requestId,
        method: req.method,
        path: req.nextUrl.pathname,
        statusCode: response.status,
        duration,
        timestamp: new Date().toISOString(),
        userAgent,
        ip,
        memoryUsage: {
          rss: endMemory.rss - startMemory.rss,
          heapTotal: endMemory.heapTotal - startMemory.heapTotal,
          heapUsed: endMemory.heapUsed - startMemory.heapUsed,
          external: endMemory.external - startMemory.external,
          arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers,
        },
        dbQueries: dbMetrics.count,
        dbQueryTime: dbMetrics.totalTime,
        ...(error && {
          errorType: error.name,
          errorMessage: error.message,
        }),
      };

      // Store the metric
      performanceStore.add(metric);

      // Add performance headers in development
      if (process.env.NODE_ENV === "development") {
        response.headers.set("X-Performance-Duration", `${duration}ms`);
        response.headers.set("X-Performance-DB-Queries", dbMetrics.count.toString());
        response.headers.set("X-Performance-DB-Time", `${dbMetrics.totalTime}ms`);
        response.headers.set("X-Performance-Request-ID", requestId);
      }

      // Log performance data
      const logLevel = response.status >= 400 ? "error" : duration > performanceThresholds.slowRequest ? "warn" : "info";
      logger[logLevel]("performance", `API Performance: ${req.method} ${req.nextUrl.pathname}`, {
        requestId,
        duration,
        statusCode: response.status,
        dbQueries: dbMetrics.count,
        dbQueryTime: dbMetrics.totalTime,
        memoryDelta: metric.memoryUsage ? Math.round(metric.memoryUsage.heapUsed / 1024 / 1024 * 100) / 100 : 0, // MB
        ...(error && { error: error.message }),
      });

      return response;
    };
  };
}

// Performance monitoring API endpoints
export function getPerformanceMetrics() {
  return {
    recent: performanceStore.getMetrics(50),
    averageResponseTime: performanceStore.getAverageResponseTime(),
    errorRate: performanceStore.getErrorRate(),
    slowRequestCount: performanceStore.getSlowRequestCount(),
    thresholds: performanceThresholds,
    timestamp: new Date().toISOString(),
  };
}

// Health check with performance data
export function getPerformanceHealth() {
  const avgResponseTime = performanceStore.getAverageResponseTime();
  const errorRate = performanceStore.getErrorRate();
  const slowRequests = performanceStore.getSlowRequestCount();

  const issues: string[] = [];

  if (avgResponseTime > performanceThresholds.slowRequest) {
    issues.push(`High average response time: ${avgResponseTime}ms`);
  }

  if (errorRate > 5) { // 5% error rate threshold
    issues.push(`High error rate: ${errorRate}%`);
  }

  if (slowRequests > 10) { // More than 10 slow requests in 5 minutes
    issues.push(`Too many slow requests: ${slowRequests}`);
  }

  return {
    healthy: issues.length === 0,
    issues,
    metrics: {
      averageResponseTime: avgResponseTime,
      errorRate: errorRate,
      slowRequestCount: slowRequests,
    },
  };
}

// Wrapper function to apply performance monitoring to API route handlers
export function withPerformanceMonitoring<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  const monitor = createPerformanceMonitor();
  return monitor(handler as any) as (...args: T) => Promise<R>;
}
