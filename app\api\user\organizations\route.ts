import { NextResponse } from "next/server";
import { getServerSession } from "@/lib/session";
import { getUserOrganizations } from "@/lib/db";

export async function GET(req: Request) {
  try {
    // Check authentication
    const session = await getServerSession();
    if (!session) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get user's organizations
    const organizations = await getUserOrganizations(session.userId);

    return NextResponse.json({
      success: true,
      organizations: organizations.map(org => ({
        id: org.id,
        name: org.name,
        type: org.type,
        status: org.status,
        membershipRole: org.membershipRole,
        joinedAt: org.joinedAt,
      })),
    });
  } catch (error) {
    console.error("Get user organizations error:", error);
    return NextResponse.json(
      { error: "Something went wrong. Please try again." },
      { status: 500 }
    );
  }
}
