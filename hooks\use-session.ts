"use client"

import { useState, useEffect } from "react"
import { SessionContext } from "@/lib/auth-config"

/**
 * React hook for accessing session context in client components
 * Provides session data and loading state
 */
export function useSession() {
  const [session, setSession] = useState<SessionContext | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSession = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch('/api/auth/session', {
          credentials: 'include'
        })

        if (response.ok) {
          const data = await response.json()
          setSession(data.session)
        } else if (response.status === 401) {
          setSession(null)
        } else {
          throw new Error('Failed to fetch session')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setSession(null)
      } finally {
        setIsLoading(false)
      }
    }

    fetchSession()
  }, [])

  return {
    session,
    isLoading,
    error,
    isAuthenticated: !!session
  }
}

/**
 * Hook for checking specific permissions
 */
export function usePermissions() {
  const { session } = useSession()

  const hasPermission = (permission: string) => {
    return session?.permissions.includes(permission as any) || false
  }

  const hasAnyPermission = (permissions: string[]) => {
    return permissions.some(permission => hasPermission(permission))
  }

  const hasAllPermissions = (permissions: string[]) => {
    return permissions.every(permission => hasPermission(permission))
  }

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissions: session?.permissions || []
  }
}

/**
 * Hook for role-based access control
 */
export function useRole() {
  const { session } = useSession()

  const hasRole = (role: string) => {
    return session?.role === role
  }

  const hasAnyRole = (roles: string[]) => {
    return roles.includes(session?.role || '')
  }

  return {
    role: session?.role,
    hasRole,
    hasAnyRole,
    isIndividualUser: hasRole('individual_user'),
    isCertProvider: hasRole('cert_provider'),
    isYachtCompany: hasRole('yacht_company'),
    isSystemAdmin: hasRole('system_admin')
  }
}
