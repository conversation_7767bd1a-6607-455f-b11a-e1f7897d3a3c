import { logger } from "@/lib/logger";
import { 
  getUsersPendingDeletion, 
  deleteUserPermanently, 
  createCleanupJob, 
  update<PERSON>leanup<PERSON>ob,
  getCleanupJobs 
} from "@/lib/db";

// Job types
export type JobType = 
  | 'account_deletion' 
  | 'file_cleanup' 
  | 'expired_tokens' 
  | 'audit_cleanup' 
  | 'performance_cleanup';

// Job status
export type JobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// Job result interface
export interface JobResult {
  success: boolean;
  processed: number;
  errors: number;
  duration: number;
  details?: any;
  errorMessages?: string[];
}

// Base job interface
export interface CronJob {
  id: string;
  type: JobType;
  status: JobStatus;
  startedAt?: Date;
  completedAt?: Date;
  result?: JobResult;
}

// Account deletion cleanup job
export async function runAccountDeletionCleanup(): Promise<JobResult> {
  const startTime = Date.now();
  const jobId = `cleanup_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  logger.info("cron", "Starting account deletion cleanup job", { jobId });
  
  try {
    // Create job record
    await createCleanupJob({
      id: jobId,
      jobType: 'account_deletion',
      status: 'running',
      startedAt: new Date(),
      metadata: { startTime },
    });
    
    // Get users pending deletion (past their recovery period)
    const usersPendingDeletion = await getUsersPendingDeletion();
    
    logger.info("cron", `Found ${usersPendingDeletion.length} users pending deletion`, { 
      jobId, 
      userCount: usersPendingDeletion.length 
    });
    
    let processed = 0;
    let errors = 0;
    const errorMessages: string[] = [];
    
    for (const user of usersPendingDeletion) {
      try {
        logger.info("cron", `Deleting user permanently`, { 
          jobId, 
          userId: user.id, 
          email: user.email 
        });
        
        await deleteUserPermanently(user.id);
        processed++;
        
        logger.info("cron", `User deleted successfully`, { 
          jobId, 
          userId: user.id 
        });
        
      } catch (error) {
        errors++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        errorMessages.push(`User ${user.id}: ${errorMessage}`);
        
        logger.error("cron", `Failed to delete user`, { 
          jobId, 
          userId: user.id, 
          error: errorMessage 
        });
      }
    }
    
    const duration = Date.now() - startTime;
    const result: JobResult = {
      success: errors === 0,
      processed,
      errors,
      duration,
      details: {
        totalUsers: usersPendingDeletion.length,
        deletedUsers: processed,
        failedUsers: errors,
      },
      ...(errorMessages.length > 0 && { errorMessages }),
    };
    
    // Update job record
    await updateCleanupJob(jobId, {
      status: result.success ? 'completed' : 'failed',
      completedAt: new Date(),
      result,
    });
    
    logger.info("cron", "Account deletion cleanup job completed", { 
      jobId, 
      result 
    });
    
    return result;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    const result: JobResult = {
      success: false,
      processed: 0,
      errors: 1,
      duration,
      errorMessages: [errorMessage],
    };
    
    // Update job record
    try {
      await updateCleanupJob(jobId, {
        status: 'failed',
        completedAt: new Date(),
        result,
      });
    } catch (updateError) {
      logger.error("cron", "Failed to update job record", { 
        jobId, 
        error: updateError instanceof Error ? updateError.message : String(updateError) 
      });
    }
    
    logger.error("cron", "Account deletion cleanup job failed", { 
      jobId, 
      error: errorMessage 
    });
    
    return result;
  }
}

// Expired tokens cleanup job
export async function runExpiredTokensCleanup(): Promise<JobResult> {
  const startTime = Date.now();
  const jobId = `tokens_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  logger.info("cron", "Starting expired tokens cleanup job", { jobId });
  
  try {
    await createCleanupJob({
      id: jobId,
      jobType: 'expired_tokens',
      status: 'running',
      startedAt: new Date(),
      metadata: { startTime },
    });
    
    // Import database functions dynamically to avoid circular dependencies
    const { cleanupExpiredTokens } = await import("@/lib/db");
    
    const result = await cleanupExpiredTokens();
    const duration = Date.now() - startTime;
    
    const jobResult: JobResult = {
      success: true,
      processed: result.deletedTokens,
      errors: 0,
      duration,
      details: {
        emailVerificationTokens: result.emailVerificationTokens,
        passwordResetTokens: result.passwordResetTokens,
        deletionTokens: result.deletionTokens,
      },
    };
    
    await updateCleanupJob(jobId, {
      status: 'completed',
      completedAt: new Date(),
      result: jobResult,
    });
    
    logger.info("cron", "Expired tokens cleanup job completed", { 
      jobId, 
      result: jobResult 
    });
    
    return jobResult;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    const result: JobResult = {
      success: false,
      processed: 0,
      errors: 1,
      duration,
      errorMessages: [errorMessage],
    };
    
    try {
      await updateCleanupJob(jobId, {
        status: 'failed',
        completedAt: new Date(),
        result,
      });
    } catch (updateError) {
      logger.error("cron", "Failed to update job record", { 
        jobId, 
        error: updateError instanceof Error ? updateError.message : String(updateError) 
      });
    }
    
    logger.error("cron", "Expired tokens cleanup job failed", { 
      jobId, 
      error: errorMessage 
    });
    
    return result;
  }
}

// File cleanup job (orphaned files)
export async function runFileCleanup(): Promise<JobResult> {
  const startTime = Date.now();
  const jobId = `files_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  logger.info("cron", "Starting file cleanup job", { jobId });
  
  try {
    await createCleanupJob({
      id: jobId,
      jobType: 'file_cleanup',
      status: 'running',
      startedAt: new Date(),
      metadata: { startTime },
    });
    
    // Import database functions dynamically
    const { cleanupOrphanedFiles } = await import("@/lib/db");
    
    const result = await cleanupOrphanedFiles();
    const duration = Date.now() - startTime;
    
    const jobResult: JobResult = {
      success: true,
      processed: result.deletedFiles,
      errors: result.errors,
      duration,
      details: {
        orphanedFiles: result.orphanedFiles,
        deletedFiles: result.deletedFiles,
        failedDeletions: result.errors,
      },
      ...(result.errorMessages && { errorMessages: result.errorMessages }),
    };
    
    await updateCleanupJob(jobId, {
      status: result.errors > 0 ? 'completed' : 'completed',
      completedAt: new Date(),
      result: jobResult,
    });
    
    logger.info("cron", "File cleanup job completed", { 
      jobId, 
      result: jobResult 
    });
    
    return jobResult;
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    const result: JobResult = {
      success: false,
      processed: 0,
      errors: 1,
      duration,
      errorMessages: [errorMessage],
    };
    
    try {
      await updateCleanupJob(jobId, {
        status: 'failed',
        completedAt: new Date(),
        result,
      });
    } catch (updateError) {
      logger.error("cron", "Failed to update job record", { 
        jobId, 
        error: updateError instanceof Error ? updateError.message : String(updateError) 
      });
    }
    
    logger.error("cron", "File cleanup job failed", { 
      jobId, 
      error: errorMessage 
    });
    
    return result;
  }
}

// Run all cleanup jobs
export async function runAllCleanupJobs(): Promise<{ [key in JobType]?: JobResult }> {
  logger.info("cron", "Starting all cleanup jobs");
  
  const results: { [key in JobType]?: JobResult } = {};
  
  try {
    // Run jobs sequentially to avoid database conflicts
    results.account_deletion = await runAccountDeletionCleanup();
    results.expired_tokens = await runExpiredTokensCleanup();
    results.file_cleanup = await runFileCleanup();
    
    const totalProcessed = Object.values(results).reduce((sum, r) => sum + (r?.processed || 0), 0);
    const totalErrors = Object.values(results).reduce((sum, r) => sum + (r?.errors || 0), 0);
    const allSuccessful = Object.values(results).every(r => r?.success);
    
    logger.info("cron", "All cleanup jobs completed", {
      totalProcessed,
      totalErrors,
      allSuccessful,
      results,
    });
    
  } catch (error) {
    logger.error("cron", "Error running cleanup jobs", {
      error: error instanceof Error ? error.message : String(error),
    });
  }
  
  return results;
}

// Get job status and history
export async function getJobStatus(limit = 10) {
  try {
    const jobs = await getCleanupJobs(limit);
    
    return {
      success: true,
      jobs: jobs.map(job => ({
        id: job.id,
        type: job.jobType,
        status: job.status,
        startedAt: job.startedAt,
        completedAt: job.completedAt,
        duration: job.result?.duration,
        processed: job.result?.processed,
        errors: job.result?.errors,
      })),
    };
  } catch (error) {
    logger.error("cron", "Failed to get job status", {
      error: error instanceof Error ? error.message : String(error),
    });
    
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Schedule information
export const cronSchedules = {
  account_deletion: {
    schedule: "0 2 * * *", // Daily at 2 AM
    description: "Clean up accounts past their recovery period",
    timezone: "UTC",
  },
  expired_tokens: {
    schedule: "0 3 * * *", // Daily at 3 AM
    description: "Remove expired verification and reset tokens",
    timezone: "UTC",
  },
  file_cleanup: {
    schedule: "0 4 * * 0", // Weekly on Sunday at 4 AM
    description: "Clean up orphaned files",
    timezone: "UTC",
  },
} as const;
